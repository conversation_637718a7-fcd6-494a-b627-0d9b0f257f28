.loading,
.loading > div {
  position: relative;
  box-sizing: border-box;
}

.loading {
  display: block;
  font-size: 0;
  // color: var(--link-color-brand-sec-small-text);

  // &.la-dark {
  //   color: var(--link-color-brand-sec-small-text);
  // }

  > div {
    display: inline-block;
    float: none;
    background-color: var(--link-color-brand-sec-legal);
    border: 0 solid var(--link-color-brand-sec-legal);
  }

  // 默认尺寸
  width: 48px;
  height: 18px;
  text-align: center;

  > div:nth-child(1) {
    animation-delay: -200ms;
  }

  > div:nth-child(2) {
    animation-delay: -100ms;
  }

  > div:nth-child(3) {
    animation-delay: 0ms;
  }

  > div {
    width: 8px;
    height: 8px;
    margin: 3px;
    border-radius: 100%;
    animation: ball-pulse 1s ease infinite;
  }

  // 小尺寸
  &.la-sm {
    width: 26px;
    height: 8px;

    > div {
      width: 4px;
      height: 4px;
      margin: 2px;
    }
  }

  // 2倍尺寸
  &.la-2x {
    width: 108px;
    height: 36px;

    > div {
      width: 20px;
      height: 20px;
      margin: 8px;
    }
  }

  // 3倍尺寸
  &.la-3x {
    width: 162px;
    height: 54px;

    > div {
      width: 30px;
      height: 30px;
      margin: 12px;
    }
  }
}

@keyframes ball-pulse {
  0%,
  60%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  30% {
    opacity: 0.1;
    transform: scale(0.01);
  }
}
